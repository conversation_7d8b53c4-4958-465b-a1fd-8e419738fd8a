<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0, minimum-scale=1.0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">
    <title><PERSON>do vs Nasser - Epic Battle</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            user-select: none;
            -webkit-user-select: none;
            -webkit-touch-callout: none;
        }

        body {
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
            height: 100vh;
            height: 100dvh; /* Dynamic viewport height for mobile */
            position: relative;
            touch-action: none; /* Prevent default touch behaviors */
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
            height: 100dvh; /* Dynamic viewport height for mobile */
            overflow: hidden;
        }

        canvas {
            display: block;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 50% 50%, #0a0a1f, #050510);
        }

        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 100;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .health-bar {
            width: 120px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .health-fill {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 8px;
        }

        #playerHealth .health-fill {
            background: linear-gradient(90deg, #4ade80, #22c55e);
        }

        #enemyHealth .health-fill {
            background: linear-gradient(90deg, #ef4444, #dc2626);
        }

        .player-name {
            color: #4ade80;
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .enemy-name {
            color: #ef4444;
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 5px;
        }

        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            z-index: 100;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .joystick {
            width: 140px;
            height: 140px;
            background: rgba(255, 255, 255, 0.15);
            border: 4px solid rgba(255, 255, 255, 0.4);
            border-radius: 50%;
            position: relative;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .joystick-knob {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 6px 20px rgba(6, 182, 212, 0.5);
            transition: all 0.1s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .fire-button {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            border: none;
            border-radius: 50%;
            color: white;
            font-weight: bold;
            font-size: 18px;
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.5);
            backdrop-filter: blur(15px);
            border: 4px solid rgba(255, 255, 255, 0.4);
            touch-action: manipulation;
        }

        .fire-button:active {
            transform: scale(0.9);
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.7);
        }

        #gameOver {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 200;
        }

        .game-over-content {
            text-align: center;
            color: white;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .game-over-content h1 {
            font-size: 32px;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #06b6d4, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .restart-btn {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            border: none;
            color: white;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 10px;
            margin-top: 20px;
            cursor: pointer;
        }

        /* Particle effects */
        .star {
            position: absolute;
            background: white;
            border-radius: 50%;
            animation: twinkle 2s infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }

        /* Start Screen */
        #startScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 300;
            backdrop-filter: blur(10px);
        }

        .start-content {
            text-align: center;
            color: white;
            padding: 30px;
        }

        .game-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #4ade80, #06b6d4, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 30px rgba(74, 222, 128, 0.5);
        }

        .game-subtitle {
            font-size: 24px;
            margin-bottom: 40px;
            color: #94a3b8;
        }

        .fullscreen-btn {
            background: linear-gradient(135deg, #4ade80, #22c55e);
            border: none;
            color: white;
            padding: 20px 40px;
            font-size: 24px;
            font-weight: bold;
            border-radius: 15px;
            margin: 10px;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(74, 222, 128, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.3);
            min-width: 280px;
            transition: all 0.3s ease;
        }

        .fullscreen-btn:active {
            transform: scale(0.95);
            box-shadow: 0 4px 15px rgba(74, 222, 128, 0.6);
        }

        .start-instructions {
            margin-top: 30px;
            font-size: 16px;
            color: #64748b;
            line-height: 1.5;
        }

        /* Mobile responsiveness */
        @media (max-height: 600px) {
            .game-title {
                font-size: 36px;
            }
            .game-subtitle {
                font-size: 18px;
                margin-bottom: 20px;
            }
            .fullscreen-btn {
                padding: 15px 30px;
                font-size: 20px;
                min-width: 240px;
            }
            .start-instructions {
                font-size: 14px;
                margin-top: 20px;
            }
        }

        @media (max-width: 400px) {
            .joystick {
                width: 120px;
                height: 120px;
            }
            .joystick-knob {
                width: 50px;
                height: 50px;
            }
            .fire-button {
                width: 85px;
                height: 85px;
                font-size: 16px;
            }
            #controls {
                bottom: 15px;
                left: 15px;
                right: 15px;
            }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>

        <!-- Start Screen -->
        <div id="startScreen">
            <div class="start-content">
                <h1 class="game-title">DODO vs NASSER</h1>
                <p class="game-subtitle">Epic Mobile Battle</p>
                <button id="fullscreenBtn" class="fullscreen-btn">🚀 START FULLSCREEN</button>
                <div class="start-instructions">
                    <p>• Use joystick to move DODO</p>
                    <p>• Hold FIRE for rapid auto-targeting</p>
                    <p>• Move and shoot simultaneously!</p>
                    <p>• 📈 NASSER gets stronger over time!</p>
                    <p>• 🧠 He evolves every 20 seconds!</p>
                    <p>• 💀 Survive his 5 intelligence levels!</p>
                </div>
            </div>
        </div>

        <div id="ui">
            <div>
                <div class="player-name">DODO</div>
                <div id="playerHealth" class="health-bar">
                    <div class="health-fill" style="width: 100%"></div>
                </div>
            </div>
            <div>
                <div class="enemy-name">NASSER</div>
                <div id="enemyHealth" class="health-bar">
                    <div class="health-fill" style="width: 100%"></div>
                </div>
            </div>
        </div>

        <div id="controls">
            <div id="joystick" class="joystick">
                <div id="joystickKnob" class="joystick-knob"></div>
            </div>
            <button id="fireButton" class="fire-button">FIRE</button>
        </div>

        <div id="gameOver">
            <div class="game-over-content">
                <h1 id="gameOverTitle">Game Over</h1>
                <p id="gameOverText">The battle is complete!</p>
                <button id="restartBtn" class="restart-btn">RESTART BATTLE</button>
            </div>
        </div>
    </div>

    <script>
        class Game {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.setupCanvas();

                this.player = new Player(100, this.canvas.height / 2);
                this.enemy = new Enemy(this.canvas.width - 100, this.canvas.height / 2);
                this.bullets = [];
                this.particles = [];
                this.explosions = [];

                this.gameRunning = false; // Start paused
                this.gameStarted = false;
                this.lastTime = 0;

                this.setupControls();
                this.setupStartScreen();
                this.createStars();
                this.gameLoop(0);
            }

            setupCanvas() {
                this.canvas.width = window.innerWidth;
                this.canvas.height = window.innerHeight;

                window.addEventListener('resize', () => {
                    this.canvas.width = window.innerWidth;
                    this.canvas.height = window.innerHeight;
                });
            }

            setupStartScreen() {
                const fullscreenBtn = document.getElementById('fullscreenBtn');
                fullscreenBtn.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    this.startGame();
                });
                fullscreenBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.startGame();
                });
            }

            async startGame() {
                // Request fullscreen
                try {
                    const element = document.documentElement;
                    if (element.requestFullscreen) {
                        await element.requestFullscreen();
                    } else if (element.webkitRequestFullscreen) {
                        await element.webkitRequestFullscreen();
                    } else if (element.mozRequestFullScreen) {
                        await element.mozRequestFullScreen();
                    } else if (element.msRequestFullscreen) {
                        await element.msRequestFullscreen();
                    }
                } catch (err) {
                    console.log('Fullscreen not supported or denied');
                }

                // Hide start screen and start game
                document.getElementById('startScreen').style.display = 'none';
                this.gameRunning = true;
                this.gameStarted = true;

                // Reset game state
                this.player = new Player(100, this.canvas.height / 2);
                this.enemy = new Enemy(this.canvas.width - 100, this.canvas.height / 2);
                this.bullets = [];
                this.particles = [];
                this.explosions = [];
            }

            createStars() {
                for (let i = 0; i < 50; i++) {
                    const star = document.createElement('div');
                    star.className = 'star';
                    star.style.left = Math.random() * 100 + '%';
                    star.style.top = Math.random() * 100 + '%';
                    star.style.width = star.style.height = Math.random() * 3 + 1 + 'px';
                    star.style.animationDelay = Math.random() * 2 + 's';
                    document.getElementById('gameContainer').appendChild(star);
                }
            }

            setupControls() {
                // Multi-touch support for simultaneous movement and shooting
                let joystickTouchId = null;
                let joystickCenter = { x: 0, y: 0 };
                let fireButtonPressed = false;
                let fireInterval = null;
                const joystick = document.getElementById('joystick');
                const joystickKnob = document.getElementById('joystickKnob');
                const fireButton = document.getElementById('fireButton');

                // Track active touches
                const activeTouches = new Map();

                const handleTouchStart = (e) => {
                    e.preventDefault();

                    for (let i = 0; i < e.changedTouches.length; i++) {
                        const touch = e.changedTouches[i];
                        const element = document.elementFromPoint(touch.clientX, touch.clientY);

                        // Check if touch is on joystick
                        if (element === joystick || joystick.contains(element)) {
                            joystickTouchId = touch.identifier;
                            const rect = joystick.getBoundingClientRect();
                            joystickCenter.x = rect.left + rect.width / 2;
                            joystickCenter.y = rect.top + rect.height / 2;
                            activeTouches.set(touch.identifier, 'joystick');
                        }
                        // Check if touch is on fire button
                        else if (element === fireButton || fireButton.contains(element)) {
                            if (this.gameRunning && !fireButtonPressed) {
                                fireButtonPressed = true;
                                // Immediate first shot
                                this.player.shoot();
                                // Start continuous firing
                                fireInterval = setInterval(() => {
                                    if (fireButtonPressed && this.gameRunning) {
                                        this.player.shoot();
                                    }
                                }, 150); // Fire every 150ms while held
                            }
                            activeTouches.set(touch.identifier, 'fire');
                        }
                    }
                };

                const handleTouchMove = (e) => {
                    e.preventDefault();

                    for (let i = 0; i < e.touches.length; i++) {
                        const touch = e.touches[i];

                        // Only handle joystick movement for the joystick touch
                        if (touch.identifier === joystickTouchId) {
                            const dx = touch.clientX - joystickCenter.x;
                            const dy = touch.clientY - joystickCenter.y;
                            const maxDistance = 40;
                            const distance = Math.min(maxDistance, Math.sqrt(dx * dx + dy * dy));
                            const angle = Math.atan2(dy, dx);

                            const knobX = Math.cos(angle) * distance;
                            const knobY = Math.sin(angle) * distance;

                            joystickKnob.style.transform = `translate(${knobX - 30}px, ${knobY - 30}px)`;

                            this.player.moveX = knobX / maxDistance;
                            this.player.moveY = knobY / maxDistance;
                        }
                    }
                };

                const handleTouchEnd = (e) => {
                    e.preventDefault();

                    for (let i = 0; i < e.changedTouches.length; i++) {
                        const touch = e.changedTouches[i];
                        const touchType = activeTouches.get(touch.identifier);

                        // Reset joystick if this was the joystick touch
                        if (touch.identifier === joystickTouchId) {
                            joystickTouchId = null;
                            joystickKnob.style.transform = 'translate(-30px, -30px)';
                            this.player.moveX = 0;
                            this.player.moveY = 0;
                        }

                        // Stop firing if this was a fire button touch
                        if (touchType === 'fire') {
                            fireButtonPressed = false;
                            if (fireInterval) {
                                clearInterval(fireInterval);
                                fireInterval = null;
                            }
                        }

                        activeTouches.delete(touch.identifier);
                    }
                };

                // Add global touch event listeners for multi-touch support
                document.addEventListener('touchstart', handleTouchStart, { passive: false });
                document.addEventListener('touchmove', handleTouchMove, { passive: false });
                document.addEventListener('touchend', handleTouchEnd, { passive: false });

                // Restart button
                document.getElementById('restartBtn').addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    this.restart();
                }, { passive: false });
            }

            gameLoop(currentTime) {
                const deltaTime = (currentTime - this.lastTime) / 1000;
                this.lastTime = currentTime;

                if (this.gameRunning) {
                    this.update(deltaTime);
                    this.render();
                }

                requestAnimationFrame((time) => this.gameLoop(time));
            }

            update(deltaTime) {
                this.player.update(deltaTime, this.canvas);
                this.enemy.update(deltaTime, this.canvas, this.player);

                // Update bullets
                this.bullets = this.bullets.filter(bullet => {
                    bullet.update(deltaTime, this.canvas);
                    return bullet.active;
                });

                // Update particles
                this.particles = this.particles.filter(particle => {
                    particle.update(deltaTime);
                    return particle.life > 0;
                });

                // Update explosions
                this.explosions = this.explosions.filter(explosion => {
                    explosion.update(deltaTime);
                    return explosion.life > 0;
                });

                // Collision detection
                this.checkCollisions();

                // Check game over
                if (this.player.health <= 0 || this.enemy.health <= 0) {
                    this.endGame();
                }
            }

            checkCollisions() {
                this.bullets.forEach(bullet => {
                    if (bullet.owner === 'player') {
                        if (this.checkBulletHit(bullet, this.enemy)) {
                            this.enemy.takeDamage(25);
                            this.createExplosion(bullet.x, bullet.y, '#ff6b6b');
                            bullet.active = false;
                        }
                    } else {
                        if (this.checkBulletHit(bullet, this.player)) {
                            // Nasser learns from successful hits
                            this.enemy.successfulHits++;

                            // Damage scales with intelligence level
                            const damage = 20 + (this.enemy.intelligenceLevel - 1) * 5;
                            this.player.takeDamage(damage);
                            this.createExplosion(bullet.x, bullet.y, '#4ade80');
                            bullet.active = false;
                        }
                    }
                });
            }

            checkBulletHit(bullet, target) {
                const dx = bullet.x - target.x;
                const dy = bullet.y - target.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                return distance < target.radius + bullet.radius;
            }

            createExplosion(x, y, color) {
                this.explosions.push(new Explosion(x, y, color));
                
                // Create particles
                for (let i = 0; i < 10; i++) {
                    this.particles.push(new Particle(x, y, color));
                }
            }

            render() {
                // Clear canvas with gradient
                const gradient = this.ctx.createRadialGradient(
                    this.canvas.width / 2, this.canvas.height / 2, 0,
                    this.canvas.width / 2, this.canvas.height / 2, this.canvas.width
                );
                gradient.addColorStop(0, '#0a0a1f');
                gradient.addColorStop(1, '#050510');
                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // Render game objects
                this.particles.forEach(particle => particle.render(this.ctx));
                this.explosions.forEach(explosion => explosion.render(this.ctx));
                this.bullets.forEach(bullet => bullet.render(this.ctx));
                this.player.render(this.ctx);
                this.enemy.render(this.ctx);

                // Update health bars
                this.updateHealthBars();
            }

            updateHealthBars() {
                const playerHealthFill = document.querySelector('#playerHealth .health-fill');
                const enemyHealthFill = document.querySelector('#enemyHealth .health-fill');
                
                playerHealthFill.style.width = Math.max(0, this.player.health) + '%';
                enemyHealthFill.style.width = Math.max(0, this.enemy.health) + '%';
            }

            endGame() {
                this.gameRunning = false;
                const gameOverDiv = document.getElementById('gameOver');
                const title = document.getElementById('gameOverTitle');
                const text = document.getElementById('gameOverText');

                if (this.player.health <= 0) {
                    title.textContent = 'NASSER WINS!';
                    text.textContent = 'The AI has prevailed in this epic battle!';
                } else {
                    title.textContent = 'DODO WINS!';
                    text.textContent = 'Victory! You have defeated the AI!';
                }

                gameOverDiv.style.display = 'flex';
            }

            restart() {
                this.gameRunning = false;
                this.gameStarted = false;
                this.player = new Player(100, this.canvas.height / 2);
                this.enemy = new Enemy(this.canvas.width - 100, this.canvas.height / 2);
                this.bullets = [];
                this.particles = [];
                this.explosions = [];
                document.getElementById('gameOver').style.display = 'none';
                document.getElementById('startScreen').style.display = 'flex';
            }
        }

        class Player {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.radius = 25;
                this.health = 100;
                this.moveX = 0;
                this.moveY = 0;
                this.speed = 200;
                this.lastShot = 0;
                this.shootCooldown = 120; // Reduced for better rapid fire
            }

            update(deltaTime, canvas) {
                this.x += this.moveX * this.speed * deltaTime;
                this.y += this.moveY * this.speed * deltaTime;

                // Keep in bounds
                this.x = Math.max(this.radius, Math.min(canvas.width - this.radius, this.x));
                this.y = Math.max(this.radius, Math.min(canvas.height - this.radius, this.y));
            }

            shoot() {
                const now = Date.now();
                if (now - this.lastShot > this.shootCooldown) {
                    // Auto-target the enemy
                    const dx = game.enemy.x - this.x;
                    const dy = game.enemy.y - this.y;
                    const angle = Math.atan2(dy, dx);
                    const bulletSpeed = 400;
                    const vx = Math.cos(angle) * bulletSpeed;
                    const vy = Math.sin(angle) * bulletSpeed;

                    game.bullets.push(new Bullet(this.x + this.radius, this.y, vx, vy, 'player'));
                    this.lastShot = now;
                }
            }

            takeDamage(damage) {
                this.health -= damage;
                this.health = Math.max(0, this.health);
            }

            render(ctx) {
                // Player glow
                ctx.shadowBlur = 20;
                ctx.shadowColor = '#4ade80';
                
                // Player body
                ctx.fillStyle = 'linear-gradient(135deg, #4ade80, #22c55e)';
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                
                const gradient = ctx.createRadialGradient(this.x, this.y, 0, this.x, this.y, this.radius);
                gradient.addColorStop(0, '#4ade80');
                gradient.addColorStop(1, '#22c55e');
                ctx.fillStyle = gradient;
                ctx.fill();

                // Player details
                ctx.shadowBlur = 0;
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('DODO', this.x, this.y + 4);
            }
        }

        class Enemy {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.radius = 25;
                this.health = 100;
                this.maxHealth = 100;

                // Base stats that will scale over time - START WEAK
                this.baseSpeed = 120; // Start slower than before
                this.speed = 120;
                this.baseShootCooldown = 1200; // Start very slow (0.8 bullets per second)
                this.shootCooldown = 1200;
                this.lastShot = 0;

                // AI Intelligence System
                this.battleTime = 0;
                this.intelligenceLevel = 1; // 1-5 levels
                this.adaptationRate = 1.0;

                // Movement AI - Start passive, become aggressive over time
                this.direction = Math.random() * Math.PI * 2;
                this.changeDirectionTimer = 0;
                this.aggressionLevel = 0.2; // Start very passive
                this.evasionMode = false;
                this.evasionTimer = 0;
                this.chargeMode = false; // Start with random movement

                // Combat AI
                this.playerTrackingAccuracy = 0.7;
                this.predictionSkill = 0.2;
                this.tacticalRetreatThreshold = 30;

                // Advanced behaviors
                this.lastPlayerPosition = { x: 0, y: 0 };
                this.playerVelocityHistory = [];
                this.dodgeAttempts = 0;
                this.successfulHits = 0;
                this.shotsFired = 0;

                // Battle phases
                this.currentPhase = 'AGGRESSIVE';
                this.phaseTimer = 0;
            }

            update(deltaTime, canvas, player) {
                this.battleTime += deltaTime;
                this.changeDirectionTimer += deltaTime;
                this.phaseTimer += deltaTime;

                // Evolve intelligence over time
                this.evolveIntelligence();

                // Track player for predictive AI
                this.trackPlayer(player);

                // Determine current battle phase
                this.updateBattlePhase();

                // Advanced AI movement
                this.updateMovement(deltaTime, canvas, player);

                // Smart combat system
                this.updateCombat(deltaTime, player);

                // Apply movement
                this.x += Math.cos(this.direction) * this.speed * deltaTime;
                this.y += Math.sin(this.direction) * this.speed * deltaTime;

                // Keep in bounds with smart boundary behavior
                this.handleBoundaries(canvas);
            }

            evolveIntelligence() {
                // Intelligence levels based on battle time - GRADUAL PROGRESSION
                const timeBasedLevel = Math.min(5, Math.floor(this.battleTime / 20) + 1); // Slower evolution (20 seconds per level)
                const performanceBonus = this.shotsFired > 0 ? (this.successfulHits / this.shotsFired) * 1 : 0;

                this.intelligenceLevel = Math.min(5, timeBasedLevel + Math.floor(performanceBonus));

                // GRADUAL power scaling - starts weak, becomes strong
                this.speed = this.baseSpeed * (1 + (this.intelligenceLevel - 1) * 0.4); // Moderate speed scaling
                this.shootCooldown = Math.max(200, this.baseShootCooldown * (1 - (this.intelligenceLevel - 1) * 0.2)); // Gradual shooting improvement
                this.playerTrackingAccuracy = Math.min(0.9, 0.5 + (this.intelligenceLevel - 1) * 0.1); // Start inaccurate
                this.predictionSkill = Math.min(0.7, 0.1 + (this.intelligenceLevel - 1) * 0.15); // Start with no prediction

                // Gradual aggression increase
                this.aggressionLevel = Math.min(0.9, 0.2 + (this.intelligenceLevel - 1) * 0.15 + (100 - this.health) / 100 * 0.2);

                // Enable charging mode only at higher levels
                this.chargeMode = this.intelligenceLevel >= 3;
            }

            trackPlayer(player) {
                // Track player velocity for prediction
                const dx = player.x - this.lastPlayerPosition.x;
                const dy = player.y - this.lastPlayerPosition.y;

                this.playerVelocityHistory.push({ dx, dy });
                if (this.playerVelocityHistory.length > 5) {
                    this.playerVelocityHistory.shift();
                }

                this.lastPlayerPosition = { x: player.x, y: player.y };
            }

            updateBattlePhase() {
                const healthPercent = this.health / this.maxHealth;

                if (healthPercent > 0.7) {
                    this.currentPhase = 'AGGRESSIVE';
                } else if (healthPercent > 0.4) {
                    this.currentPhase = 'TACTICAL';
                } else if (healthPercent > 0.2) {
                    this.currentPhase = 'DESPERATE';
                } else {
                    this.currentPhase = 'BERSERKER';
                }
            }

            updateMovement(deltaTime, canvas, player) {
                const distanceToPlayer = Math.sqrt((player.x - this.x) ** 2 + (player.y - this.y) ** 2);

                // Movement behavior based on intelligence level
                if (this.chargeMode && this.intelligenceLevel >= 3) {
                    // Higher levels: Direct charging
                    this.performDirectCharge(player);
                } else {
                    // Lower levels: Mixed movement (random + some aggression)
                    this.performBasicMovement(player, distanceToPlayer);
                }

                // Direction change frequency based on intelligence
                const directionChangeInterval = Math.max(0.5, 2.5 - this.intelligenceLevel * 0.4);
                if (this.changeDirectionTimer > directionChangeInterval) {
                    this.chooseNewDirection(player, distanceToPlayer);
                    this.changeDirectionTimer = 0;
                }
            }

            performBasicMovement(player, distanceToPlayer) {
                // Early levels: Mix of random movement and occasional aggression
                if (Math.random() < this.aggressionLevel) {
                    const dx = player.x - this.x;
                    const dy = player.y - this.y;
                    this.direction = Math.atan2(dy, dx) + (Math.random() - 0.5) * Math.PI * 0.6;
                } else {
                    // Random movement
                    this.direction = Math.random() * Math.PI * 2;
                }
            }

            performDirectCharge(player) {
                // Calculate direct path to player (for higher intelligence levels)
                const dx = player.x - this.x;
                const dy = player.y - this.y;

                // Set direction directly toward player with slight randomness
                this.direction = Math.atan2(dy, dx) + (Math.random() - 0.5) * 0.2;
            }

            shouldEvade(player) {
                // More intelligent evasion based on player behavior
                const recentPlayerActivity = Date.now() - player.lastShot < 500;
                const lowHealth = this.health < this.tacticalRetreatThreshold;
                const randomEvasion = Math.random() < 0.1 * this.intelligenceLevel;

                return recentPlayerActivity || lowHealth || randomEvasion;
            }

            performEvasion(player) {
                // Smart evasion patterns
                const angleToPlayer = Math.atan2(player.y - this.y, player.x - this.x);
                const evasionAngle = angleToPlayer + Math.PI/2 + (Math.random() - 0.5) * Math.PI;

                this.direction = evasionAngle;
                this.evasionMode = true;
                this.evasionTimer = 1.0;
                this.dodgeAttempts++;
            }

            performTacticalMovement(player, distanceToPlayer) {
                const optimalDistance = 200 + this.intelligenceLevel * 50;

                if (this.currentPhase === 'AGGRESSIVE') {
                    // Move toward player aggressively
                    if (Math.random() < this.aggressionLevel) {
                        const dx = player.x - this.x;
                        const dy = player.y - this.y;
                        this.direction = Math.atan2(dy, dx) + (Math.random() - 0.5) * Math.PI * 0.3;
                    }
                } else if (this.currentPhase === 'TACTICAL') {
                    // Maintain optimal distance
                    if (distanceToPlayer < optimalDistance) {
                        // Move away
                        const dx = this.x - player.x;
                        const dy = this.y - player.y;
                        this.direction = Math.atan2(dy, dx) + (Math.random() - 0.5) * Math.PI * 0.4;
                    } else {
                        // Circle around player
                        const angleToPlayer = Math.atan2(player.y - this.y, player.x - this.x);
                        this.direction = angleToPlayer + Math.PI/2 + (Math.random() - 0.5) * Math.PI * 0.5;
                    }
                } else if (this.currentPhase === 'DESPERATE') {
                    // Erratic movement with flanking
                    if (Math.random() < 0.7) {
                        const flankAngle = Math.atan2(player.y - this.y, player.x - this.x) + Math.PI/3 * (Math.random() > 0.5 ? 1 : -1);
                        this.direction = flankAngle;
                    }
                } else { // BERSERKER
                    // Direct aggressive approach
                    const dx = player.x - this.x;
                    const dy = player.y - this.y;
                    this.direction = Math.atan2(dy, dx) + (Math.random() - 0.5) * Math.PI * 0.2;
                }
            }

            chooseNewDirection(player, distanceToPlayer) {
                // Intelligent direction changes based on situation
                if (this.intelligenceLevel >= 3) {
                    // Predict player movement
                    const predictedPlayerPos = this.predictPlayerPosition(player);
                    const dx = predictedPlayerPos.x - this.x;
                    const dy = predictedPlayerPos.y - this.y;
                    this.direction = Math.atan2(dy, dx) + (Math.random() - 0.5) * Math.PI * (0.5 - this.predictionSkill);
                } else {
                    // Basic movement toward player
                    if (Math.random() < this.aggressionLevel) {
                        const dx = player.x - this.x;
                        const dy = player.y - this.y;
                        this.direction = Math.atan2(dy, dx) + (Math.random() - 0.5) * Math.PI * 0.5;
                    } else {
                        this.direction = Math.random() * Math.PI * 2;
                    }
                }
            }

            predictPlayerPosition(player) {
                if (this.playerVelocityHistory.length < 2) {
                    return { x: player.x, y: player.y };
                }

                // Average recent velocity
                const avgVelocity = this.playerVelocityHistory.reduce((acc, vel) => ({
                    dx: acc.dx + vel.dx,
                    dy: acc.dy + vel.dy
                }), { dx: 0, dy: 0 });

                avgVelocity.dx /= this.playerVelocityHistory.length;
                avgVelocity.dy /= this.playerVelocityHistory.length;

                // Predict future position
                const predictionTime = this.predictionSkill * 2;
                return {
                    x: player.x + avgVelocity.dx * predictionTime * 60,
                    y: player.y + avgVelocity.dy * predictionTime * 60
                };
            }

            takeDamage(damage) {
                this.health -= damage;
                this.health = Math.max(0, this.health);

                // Become more aggressive when damaged
                this.aggressionLevel = Math.min(1.0, this.aggressionLevel + 0.1);

                // Trigger evasion
                this.evasionMode = true;
                this.evasionTimer = 0.5;
            }

            updateCombat(deltaTime, player) {
                const now = Date.now();
                if (now - this.lastShot > this.shootCooldown) {
                    const dx = player.x - this.x;
                    const dy = player.y - this.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    // Shooting range increases with intelligence
                    const maxRange = 250 + this.intelligenceLevel * 75;

                    if (distance < maxRange) {
                        this.performProgressiveShooting(player, distance);
                        this.lastShot = now;
                        this.shotsFired++;
                    }
                }
            }

            performProgressiveShooting(player, distance) {
                let targetX = player.x;
                let targetY = player.y;

                // Prediction only for higher intelligence levels
                if (this.intelligenceLevel >= 2) {
                    const predicted = this.predictPlayerPosition(player);
                    targetX = predicted.x;
                    targetY = predicted.y;
                }

                // Calculate shooting angle with accuracy based on intelligence
                const baseAngle = Math.atan2(targetY - this.y, targetX - this.x);
                const accuracySpread = (1 - this.playerTrackingAccuracy) * Math.PI * 0.4; // Start inaccurate
                const finalAngle = baseAngle + (Math.random() - 0.5) * accuracySpread;

                // Bullet speed increases gradually with intelligence
                const bulletSpeed = 250 + this.intelligenceLevel * 30;
                const vx = Math.cos(finalAngle) * bulletSpeed;
                const vy = Math.sin(finalAngle) * bulletSpeed;

                // Progressive shooting patterns based on intelligence level
                if (this.intelligenceLevel <= 2) {
                    // Level 1-2: Single shot only
                    game.bullets.push(new Bullet(this.x, this.y, vx, vy, 'enemy'));
                } else if (this.intelligenceLevel === 3) {
                    // Level 3: Occasional double shot
                    game.bullets.push(new Bullet(this.x, this.y, vx, vy, 'enemy'));
                    if (Math.random() < 0.3) {
                        const spreadAngle = finalAngle + (Math.random() - 0.5) * 0.3;
                        const spreadVx = Math.cos(spreadAngle) * bulletSpeed;
                        const spreadVy = Math.sin(spreadAngle) * bulletSpeed;
                        game.bullets.push(new Bullet(this.x, this.y, spreadVx, spreadVy, 'enemy'));
                    }
                } else if (this.intelligenceLevel === 4) {
                    // Level 4: Triple shot
                    for (let i = -1; i <= 1; i++) {
                        const spreadAngle = finalAngle + i * 0.2;
                        const spreadVx = Math.cos(spreadAngle) * bulletSpeed;
                        const spreadVy = Math.sin(spreadAngle) * bulletSpeed;
                        game.bullets.push(new Bullet(this.x, this.y, spreadVx, spreadVy, 'enemy'));
                    }
                } else {
                    // Level 5: Burst fire
                    for (let i = -1; i <= 1; i++) {
                        const spreadAngle = finalAngle + i * 0.15;
                        const spreadVx = Math.cos(spreadAngle) * bulletSpeed;
                        const spreadVy = Math.sin(spreadAngle) * bulletSpeed;
                        game.bullets.push(new Bullet(this.x, this.y, spreadVx, spreadVy, 'enemy'));
                    }
                    // Delayed burst
                    setTimeout(() => {
                        const burstAngle = finalAngle + (Math.random() - 0.5) * 0.3;
                        const burstVx = Math.cos(burstAngle) * bulletSpeed;
                        const burstVy = Math.sin(burstAngle) * bulletSpeed;
                        game.bullets.push(new Bullet(this.x, this.y, burstVx, burstVy, 'enemy'));
                    }, 100);
                }
            }

            handleBoundaries(canvas) {
                const margin = this.radius + 10;

                // Smart boundary behavior
                if (this.x < margin) {
                    this.x = margin;
                    if (this.intelligenceLevel >= 2) {
                        // Use wall to tactical advantage
                        this.direction = Math.PI/4 + Math.random() * Math.PI/2;
                    } else {
                        this.direction = Math.random() * Math.PI/2;
                    }
                }
                if (this.x > canvas.width - margin) {
                    this.x = canvas.width - margin;
                    if (this.intelligenceLevel >= 2) {
                        this.direction = 3*Math.PI/4 + Math.random() * Math.PI/2;
                    } else {
                        this.direction = Math.PI/2 + Math.random() * Math.PI/2;
                    }
                }
                if (this.y < margin) {
                    this.y = margin;
                    if (this.intelligenceLevel >= 2) {
                        this.direction = -Math.PI/4 + Math.random() * Math.PI/2;
                    } else {
                        this.direction = Math.random() * Math.PI;
                    }
                }
                if (this.y > canvas.height - margin) {
                    this.y = canvas.height - margin;
                    if (this.intelligenceLevel >= 2) {
                        this.direction = -3*Math.PI/4 + Math.random() * Math.PI/2;
                    } else {
                        this.direction = Math.PI + Math.random() * Math.PI;
                    }
                }
            }

            render(ctx) {
                // Progressive appearance based on intelligence level
                const glowIntensity = 15 + this.intelligenceLevel * 8; // Gradual glow increase
                const glowColor = this.getPhaseColor();

                ctx.shadowBlur = glowIntensity;
                ctx.shadowColor = glowColor;

                // Body size increases slightly with intelligence
                const renderRadius = this.radius + (this.intelligenceLevel - 1) * 2;
                ctx.beginPath();
                ctx.arc(this.x, this.y, renderRadius, 0, Math.PI * 2);

                const gradient = ctx.createRadialGradient(this.x, this.y, 0, this.x, this.y, renderRadius);
                gradient.addColorStop(0, glowColor);
                gradient.addColorStop(1, this.getDarkerPhaseColor());
                ctx.fillStyle = gradient;
                ctx.fill();

                // Intelligence rings - only show for higher levels
                if (this.intelligenceLevel > 1) {
                    for (let i = 1; i <= Math.min(3, this.intelligenceLevel - 1); i++) {
                        ctx.shadowBlur = 5;
                        ctx.strokeStyle = `rgba(255, 68, 68, ${0.6 - i * 0.15})`;
                        ctx.lineWidth = 2 + i;
                        ctx.beginPath();
                        ctx.arc(this.x, this.y, renderRadius + 5 + i * 6, 0, Math.PI * 2);
                        ctx.stroke();
                    }
                }

                // Progressive phase indicators
                ctx.shadowBlur = 0;
                ctx.fillStyle = '#ffffff';
                ctx.font = `bold ${8 + this.intelligenceLevel}px Arial`;
                ctx.textAlign = 'center';

                // Show different indicators based on level
                let phaseText = '';
                if (this.intelligenceLevel === 1) phaseText = '😴'; // Sleepy/weak
                else if (this.intelligenceLevel === 2) phaseText = '😐'; // Neutral
                else if (this.intelligenceLevel === 3) phaseText = '😠'; // Angry
                else if (this.intelligenceLevel === 4) phaseText = '👹'; // Demon
                else phaseText = '💀'; // Skull

                ctx.fillText(`${phaseText} NASSER Lv.${this.intelligenceLevel}`, this.x, this.y + 3);

                // Show charging indicator only when actually charging
                if (this.chargeMode && this.intelligenceLevel >= 3) {
                    ctx.fillStyle = '#ffff00';
                    ctx.font = 'bold 12px Arial';
                    ctx.fillText('⚡CHARGING⚡', this.x, this.y - 20);
                }

                // Show shooting mode indicator
                if (this.intelligenceLevel >= 4) {
                    ctx.fillStyle = '#ff6666';
                    ctx.font = 'bold 10px Arial';
                    ctx.fillText('MULTI-SHOT', this.x, this.y + 18);
                }
            }

            getPhaseColor() {
                switch (this.currentPhase) {
                    case 'AGGRESSIVE': return '#ef4444';
                    case 'TACTICAL': return '#f97316';
                    case 'DESPERATE': return '#dc2626';
                    case 'BERSERKER': return '#7c2d12';
                    default: return '#ef4444';
                }
            }

            getDarkerPhaseColor() {
                switch (this.currentPhase) {
                    case 'AGGRESSIVE': return '#dc2626';
                    case 'TACTICAL': return '#ea580c';
                    case 'DESPERATE': return '#991b1b';
                    case 'BERSERKER': return '#451a03';
                    default: return '#dc2626';
                }
            }
        }

        class Bullet {
            constructor(x, y, vx, vy, owner) {
                this.x = x;
                this.y = y;
                this.vx = vx;
                this.vy = vy;
                this.radius = 4;
                this.owner = owner;
                this.active = true;
                this.trail = [];
            }

            update(deltaTime, canvas) {
                // Add to trail
                this.trail.push({ x: this.x, y: this.y });
                if (this.trail.length > 8) {
                    this.trail.shift();
                }

                this.x += this.vx * deltaTime;
                this.y += this.vy * deltaTime;

                // Remove if out of bounds
                if (this.x < 0 || this.x > canvas.width || this.y < 0 || this.y > canvas.height) {
                    this.active = false;
                }
            }

            render(ctx) {
                // Render trail
                ctx.strokeStyle = this.owner === 'player' ? '#4ade80' : '#ef4444';
                ctx.lineWidth = 2;
                ctx.globalAlpha = 0.6;
                
                if (this.trail.length > 1) {
                    ctx.beginPath();
                    ctx.moveTo(this.trail[0].x, this.trail[0].y);
                    for (let i = 1; i < this.trail.length; i++) {
                        ctx.lineTo(this.trail[i].x, this.trail[i].y);
                    }
                    ctx.stroke();
                }

                ctx.globalAlpha = 1;

                // Bullet glow
                ctx.shadowBlur = 15;
                ctx.shadowColor = this.owner === 'player' ? '#4ade80' : '#ef4444';

                // Bullet
                ctx.fillStyle = this.owner === 'player' ? '#4ade80' : '#ef4444';
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                ctx.fill();

                ctx.shadowBlur = 0;
            }
        }

        class Particle {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 200;
                this.vy = (Math.random() - 0.5) * 200;
                this.life = 1;
                this.decay = Math.random() * 2 + 1;
                this.color = color;
                this.size = Math.random() * 4 + 2;
            }

            update(deltaTime) {
                this.x += this.vx * deltaTime;
                this.y += this.vy * deltaTime;
                this.vx *= 0.98;
                this.vy *= 0.98;
                this.life -= this.decay * deltaTime;
            }

            render(ctx) {
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size * this.life, 0, Math.PI * 2);
                ctx.fill();
                ctx.globalAlpha = 1;
            }
        }

        class Explosion {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.radius = 0;
                this.maxRadius = 30;
                this.life = 1;
                this.color = color;
            }

            update(deltaTime) {
                this.radius += 100 * deltaTime;
                this.life -= 3 * deltaTime;
            }

            render(ctx) {
                ctx.globalAlpha = this.life;
                ctx.strokeStyle = this.color;
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                ctx.stroke();
                ctx.globalAlpha = 1;
            }
        }

        // Start the game
        let game;
        window.addEventListener('load', () => {
            game = new Game();
        });

        // Prevent zoom on double tap
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    </script>
</body>
</html>